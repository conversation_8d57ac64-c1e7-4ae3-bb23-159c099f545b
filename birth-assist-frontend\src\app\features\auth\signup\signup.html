<form [formGroup]="signupForm" (ngSubmit)="onSubmit()">
    <label for="email">Email</label>
    <input id="email" type="email" formControlName="email" placeholder="Email" autocomplete="email" />
    <label for="password"><PERSON><PERSON><PERSON><PERSON></label>
    <input id="password" type="password" formControlName="password" placeholder="Jelsz<PERSON>" />
    <label for="confirmPassword">Jelsz<PERSON> megerősítése</label>
    <input id="confirmPassword" type="password" formControlName="confirmPassword" placeholder="Je<PERSON><PERSON><PERSON> megerősítése" />
    <label for="firstName">Keresztnév</label>
    <input id="firstName" type="text" formControlName="firstName" placeholder="Keresztnév" />
    <label for="lastName">Vezetéknév</label>
    <input id="lastName" type="text" formControlName="lastName" placeholder="Vezetéknév" />
    <button type="submit" [disabled]="!signupForm.valid"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></button>
</form>