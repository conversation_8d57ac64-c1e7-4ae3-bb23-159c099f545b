import { Routes } from '@angular/router';
import { Home } from './features/home/<USER>';

export const routes: Routes = [
    {
    path: 'login',
    loadComponent: () => import('./features/auth/auth-layout/auth-layout').then((c) => c.AuthLayout),
    children: [
        {
            path: '', 
            loadComponent: () => import('./features/auth/login/login').then((c) => c.<PERSON>),
        }
    ]
    },
    {
        path: 'signup',
        loadComponent: () => import('./features/auth/auth-layout/auth-layout').then((c) => c.AuthLayout),
        children: [
            {
                path: '',
                loadComponent: () => import('./features/auth/signup/signup').then((c) => c.Signup)
            }
        ]
    },
    {
        path: '',
        component: Home,
        pathMatch: 'full'
    },
    {
        path: '**',
        redirectTo: '',
        component: Home
    }
];
