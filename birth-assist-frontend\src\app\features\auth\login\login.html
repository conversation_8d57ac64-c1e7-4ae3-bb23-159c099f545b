<form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
    <label for="email">Email</label>
    <input id="email" type="email" formControlName="email" placeholder="Email" autocomplete="email" />
    <label for="password"><PERSON><PERSON><PERSON><PERSON></label>
    <input id="password" type="password" formControlName="password" placeholder="Je<PERSON><PERSON><PERSON>" />
    <button type="submit" [disabled]="!loginForm.valid">Bejelentkezés</button>
</form>